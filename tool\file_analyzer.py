#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件整理分析工具
用于分析指定目录下的文件，生成Excel统计报告
"""

import os
import hashlib
import pandas as pd
from pathlib import Path
import time
from datetime import datetime
import magic
import sys

class FileAnalyzer:
    def __init__(self, base_dir):
        """
        初始化文件分析器
        
        Args:
            base_dir (str): 基础目录路径
        """
        self.base_dir = Path(base_dir)
        self.file_data = []
        self.md5_dict = {}  # 用于检测重复文件
        
    def get_file_md5(self, file_path):
        """
        计算文件的MD5值
        
        Args:
            file_path (Path): 文件路径
            
        Returns:
            str: MD5值
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"计算MD5失败: {file_path}, 错误: {e}")
            return ""
    
    def get_file_type(self, file_path):
        """
        获取文件类型
        
        Args:
            file_path (Path): 文件路径
            
        Returns:
            str: 文件类型
        """
        try:
            # 首先尝试从扩展名获取
            ext = file_path.suffix.upper().lstrip('.')
            if ext:
                return ext
            
            # 如果没有扩展名，使用magic库检测
            mime = magic.Magic(mime=True)
            file_type = mime.from_file(str(file_path))
            
            # 常见MIME类型映射
            mime_map = {
                'application/pdf': 'PDF',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCX',
                'application/msword': 'DOC',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'XLSX',
                'application/vnd.ms-excel': 'XLS',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PPTX',
                'application/vnd.ms-powerpoint': 'PPT',
                'image/jpeg': 'JPG',
                'image/png': 'PNG',
                'text/plain': 'TXT',
                'application/zip': 'ZIP',
                'application/x-rar-compressed': 'RAR'
            }
            
            return mime_map.get(file_type, file_type.split('/')[-1].upper())
            
        except Exception as e:
            print(f"获取文件类型失败: {file_path}, 错误: {e}")
            return "UNKNOWN"
    
    def get_file_creation_time(self, file_path):
        """
        获取文件创建时间
        
        Args:
            file_path (Path): 文件路径
            
        Returns:
            str: 创建时间字符串
        """
        try:
            # 在Windows上，st_ctime是创建时间
            # 在Unix上，st_ctime是状态改变时间，st_birthtime是创建时间（如果支持）
            stat = file_path.stat()
            
            if hasattr(stat, 'st_birthtime'):
                # macOS和一些Unix系统支持
                creation_time = stat.st_birthtime
            else:
                # Windows和其他系统
                creation_time = stat.st_ctime
                
            return datetime.fromtimestamp(creation_time).strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            print(f"获取创建时间失败: {file_path}, 错误: {e}")
            return ""
    
    def get_file_size_mb(self, file_path):
        """
        获取文件大小（MB）
        
        Args:
            file_path (Path): 文件路径
            
        Returns:
            float: 文件大小（MB）
        """
        try:
            size_bytes = file_path.stat().st_size
            size_mb = size_bytes / (1024 * 1024)
            return round(size_mb, 2)
        except Exception as e:
            print(f"获取文件大小失败: {file_path}, 错误: {e}")
            return 0.0
    
    def analyze_directory(self, target_dirs):
        """
        分析指定目录
        
        Args:
            target_dirs (list): 目标目录列表
        """
        print("开始分析文件...")
        
        for dir_name in target_dirs:
            dir_path = self.base_dir / dir_name
            if not dir_path.exists():
                print(f"目录不存在: {dir_path}")
                continue
                
            print(f"正在分析目录: {dir_name}")
            
            # 递归遍历目录
            for file_path in dir_path.rglob('*'):
                if file_path.is_file():
                    self.analyze_file(file_path)
        
        # 检测重复文件
        self.detect_duplicates()
        
        print(f"分析完成，共处理 {len(self.file_data)} 个文件")
    
    def analyze_file(self, file_path):
        """
        分析单个文件
        
        Args:
            file_path (Path): 文件路径
        """
        try:
            # 获取文件信息
            original_name = file_path.name
            file_size = self.get_file_size_mb(file_path)
            file_type = self.get_file_type(file_path)
            creation_time = self.get_file_creation_time(file_path)
            md5_hash = self.get_file_md5(file_path)
            full_path = str(file_path.absolute())
            
            # 存储文件信息
            file_info = {
                '原资料名': original_name,
                '文件大小': file_size,
                '文件类型': file_type,
                '创建时间': creation_time,
                'MD5': md5_hash,
                '是否重复': 'N',  # 初始值，后续会更新
                '完整路径': full_path
            }
            
            self.file_data.append(file_info)
            
            # 记录MD5用于重复检测
            if md5_hash:
                if md5_hash not in self.md5_dict:
                    self.md5_dict[md5_hash] = []
                self.md5_dict[md5_hash].append(len(self.file_data) - 1)
            
            print(f"已处理: {original_name}")
            
        except Exception as e:
            print(f"处理文件失败: {file_path}, 错误: {e}")
    
    def detect_duplicates(self):
        """
        检测重复文件
        """
        print("检测重复文件...")
        
        for md5_hash, indices in self.md5_dict.items():
            if len(indices) > 1:
                # 有重复文件
                for index in indices:
                    self.file_data[index]['是否重复'] = 'Y'
                    
                print(f"发现重复文件 (MD5: {md5_hash[:8]}...): {len(indices)} 个")
    
    def export_to_excel(self, output_file):
        """
        导出到Excel文件
        
        Args:
            output_file (str): 输出文件路径
        """
        try:
            # 创建DataFrame
            df = pd.DataFrame(self.file_data)
            
            # 创建Excel写入器
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 写入主表
                df.to_excel(writer, sheet_name='文件统计', index=False)
                
                # 创建重复文件表
                duplicate_files = df[df['是否重复'] == 'Y']
                if not duplicate_files.empty:
                    duplicate_files.to_excel(writer, sheet_name='重复文件', index=False)
                
                # 创建统计摘要表
                summary_data = {
                    '统计项目': ['总文件数', '重复文件数', '总大小(MB)', '文件类型数'],
                    '数值': [
                        len(df),
                        len(duplicate_files),
                        round(df['文件大小'].sum(), 2),
                        df['文件类型'].nunique()
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='统计摘要', index=False)
            
            print(f"Excel文件已生成: {output_file}")
            
        except Exception as e:
            print(f"导出Excel失败: {e}")

def main():
    """
    主函数
    """
    # 设置基础目录
    base_dir = Path(__file__).parent.parent
    
    # 目标目录
    target_directories = [
        "一年级上册-资料大全",
        "一年级升二年级-资料大全", 
        "一年级下册-资料大全"
    ]
    
    # 创建分析器
    analyzer = FileAnalyzer(base_dir)
    
    # 分析目录
    analyzer.analyze_directory(target_directories)
    
    # 生成输出文件名（包含时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = base_dir / "tool" / f"文件统计报告_{timestamp}.xlsx"
    
    # 导出Excel
    analyzer.export_to_excel(output_file)
    
    print("\n分析完成！")
    print(f"报告文件: {output_file}")

if __name__ == "__main__":
    main()
