#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel转CSV工具
将Excel文件转换为CSV格式
"""

import pandas as pd
from pathlib import Path

def excel_to_csv(excel_file, csv_file):
    """
    将Excel文件转换为CSV
    
    Args:
        excel_file (str): Excel文件路径
        csv_file (str): 输出CSV文件路径
    """
    try:
        # 读取Excel文件的第一个工作表
        df = pd.read_excel(excel_file, sheet_name=0)
        
        # 保存为CSV
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        print(f"转换成功!")
        print(f"源文件: {excel_file}")
        print(f"目标文件: {csv_file}")
        print(f"记录数: {len(df)}")
        
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        return False

def main():
    """
    主函数
    """
    # 设置文件路径
    base_dir = Path(__file__).parent
    excel_file = base_dir / "文件统计报告_20250821_114332.xlsx"
    csv_file = base_dir / "文件统计报告_20250821_114332.csv"
    
    # 检查Excel文件是否存在
    if not excel_file.exists():
        print(f"Excel文件不存在: {excel_file}")
        return
    
    # 执行转换
    excel_to_csv(excel_file, csv_file)

if __name__ == "__main__":
    main()
