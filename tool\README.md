# 文件整理分析工具

这个工具用于分析指定目录下的文件，生成详细的Excel统计报告。

## 功能特性

- 递归扫描指定目录下的所有文件
- 提取文件的详细信息：
  - 原资料名（保留原始文件名，包含特殊符号）
  - 文件大小（以MB为单位）
  - 文件类型（PDF、DOCX等）
  - 创建时间（文档的创建时间）
  - MD5值（用于重复检测）
  - 是否重复（Y/N标志）
  - 完整路径
- 自动检测重复文件
- 生成Excel报告，包含多个工作表：
  - 文件统计：所有文件的详细信息
  - 重复文件：仅显示重复的文件
  - 统计摘要：总体统计信息

## 使用方法

### 1. 首次使用 - 设置环境

双击运行 `setup_env.bat`，这将：
- 创建Python虚拟环境
- 安装所需的依赖包（使用清华镜像）

### 2. 运行分析

双击运行 `run_analyzer.bat`，这将：
- 激活虚拟环境
- 运行文件分析脚本
- 生成Excel报告

### 3. 查看结果

分析完成后，会在tool目录下生成一个带时间戳的Excel文件，例如：
`文件统计报告_20241221_143022.xlsx`

## 分析的目录

工具会分析以下三个目录：
- 一年级上册-资料大全
- 一年级升二年级-资料大全
- 一年级下册-资料大全

## 依赖包

- pandas: 数据处理和Excel生成
- openpyxl: Excel文件操作
- python-magic-bin: 文件类型检测

## 注意事项

1. 确保Python 3.13已正确安装
2. 首次使用前必须运行setup_env.bat设置环境
3. 分析大量文件时可能需要较长时间，请耐心等待
4. 生成的Excel文件会保存在tool目录下

## 故障排除

如果遇到问题：
1. 确认Python版本是否正确
2. 检查虚拟环境是否正确创建
3. 确认目标目录是否存在
4. 查看控制台输出的错误信息
