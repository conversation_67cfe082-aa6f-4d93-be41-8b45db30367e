#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复文件清理工具
安全地处理重复文件，将重复的文件移动到备份文件夹
"""

import os
import pandas as pd
import shutil
from pathlib import Path
from datetime import datetime
import re

class DuplicateCleaner:
    def __init__(self, csv_file_path, base_dir):
        """
        初始化重复文件清理器
        
        Args:
            csv_file_path (str): CSV文件路径
            base_dir (str): 基础目录路径
        """
        self.csv_file_path = Path(csv_file_path)
        self.base_dir = Path(base_dir)
        self.backup_dir = self.base_dir / "重复文件备份"
        self.df = None
        self.duplicate_groups = {}
        self.operation_log = []
        
        # 目录优先级（数字越小优先级越高）
        self.dir_priority = {
            "一年级上册-资料大全": 1,
            "一年级下册-资料大全": 2,
            "一年级升二年级-资料大全": 3
        }
    
    def load_csv_data(self):
        """
        加载CSV数据
        """
        try:
            print("正在加载CSV文件...")
            self.df = pd.read_csv(self.csv_file_path, encoding='utf-8')
            print(f"成功加载 {len(self.df)} 条记录")
            return True
        except Exception as e:
            print(f"加载CSV文件失败: {e}")
            return False
    
    def analyze_duplicates(self):
        """
        分析重复文件
        """
        print("正在分析重复文件...")
        
        # 筛选出重复文件
        duplicate_files = self.df[self.df['是否重复'] == 'Y'].copy()
        print(f"发现 {len(duplicate_files)} 个重复文件")
        
        # 按MD5分组
        for md5_hash, group in duplicate_files.groupby('MD5'):
            if len(group) > 1:
                self.duplicate_groups[md5_hash] = group.to_dict('records')
        
        print(f"共有 {len(self.duplicate_groups)} 组重复文件")
        
        # 统计每组的文件数量
        group_sizes = [len(group) for group in self.duplicate_groups.values()]
        print(f"重复组大小分布: 最小{min(group_sizes)}个, 最大{max(group_sizes)}个, 平均{sum(group_sizes)/len(group_sizes):.1f}个")
    
    def get_file_quality_score(self, file_info):
        """
        计算文件质量分数（分数越高质量越好）
        
        Args:
            file_info (dict): 文件信息
            
        Returns:
            int: 质量分数
        """
        score = 0
        filename = file_info['原资料名']
        filepath = file_info['完整路径']
        
        # 1. 目录优先级分数（优先级高的加分多）
        for dir_name, priority in self.dir_priority.items():
            if dir_name in filepath:
                score += (10 - priority * 2)  # 一年级上册=8分，下册=6分，升二年级=4分
                break
        
        # 2. 文件名质量分数
        # 减分项：包含数字后缀
        if re.search(r'\(\d+\)', filename):  # 如 (1), (2)
            score -= 5
        if '副本' in filename:
            score -= 5
        if filename.endswith('(1)') or filename.endswith('_1'):
            score -= 3
        
        # 加分项：文件名较短且规范
        if len(filename) < 50:
            score += 2
        
        # 3. 路径深度分数（路径越短越好）
        path_depth = len(Path(filepath).parts)
        score += max(0, 10 - path_depth)  # 路径越深扣分越多
        
        return score
    
    def select_file_to_keep(self, duplicate_group):
        """
        从重复文件组中选择要保留的文件
        
        Args:
            duplicate_group (list): 重复文件组
            
        Returns:
            dict: 要保留的文件信息
        """
        # 计算每个文件的质量分数
        scored_files = []
        for file_info in duplicate_group:
            score = self.get_file_quality_score(file_info)
            scored_files.append((score, file_info))
        
        # 按分数排序，选择分数最高的
        scored_files.sort(key=lambda x: x[0], reverse=True)
        
        return scored_files[0][1]  # 返回分数最高的文件
    
    def create_backup_directory(self):
        """
        创建备份目录
        """
        try:
            self.backup_dir.mkdir(exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_backup_dir = self.backup_dir / f"备份_{timestamp}"
            session_backup_dir.mkdir(exist_ok=True)
            self.session_backup_dir = session_backup_dir
            print(f"创建备份目录: {session_backup_dir}")
            return True
        except Exception as e:
            print(f"创建备份目录失败: {e}")
            return False

    def generate_deletion_plan(self):
        """
        生成删除计划
        """
        print("正在生成删除计划...")
        deletion_plan = []

        for md5_hash, duplicate_group in self.duplicate_groups.items():
            if len(duplicate_group) < 2:
                continue

            # 选择要保留的文件
            file_to_keep = self.select_file_to_keep(duplicate_group)

            # 其他文件标记为删除
            for file_info in duplicate_group:
                if file_info['完整路径'] != file_to_keep['完整路径']:
                    deletion_plan.append({
                        'MD5': md5_hash,
                        '要删除的文件': file_info['完整路径'],
                        '保留的文件': file_to_keep['完整路径'],
                        '文件名': file_info['原资料名'],
                        '文件大小': file_info['文件大小'],
                        '删除原因': '重复文件'
                    })

        print(f"计划删除 {len(deletion_plan)} 个重复文件")
        return deletion_plan

    def save_deletion_plan(self, deletion_plan):
        """
        保存删除计划到文件
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plan_file = self.base_dir / "tool" / f"删除计划_{timestamp}.csv"

            df_plan = pd.DataFrame(deletion_plan)
            df_plan.to_csv(plan_file, index=False, encoding='utf-8-sig')

            print(f"删除计划已保存到: {plan_file}")
            return plan_file
        except Exception as e:
            print(f"保存删除计划失败: {e}")
            return None

    def move_files_to_backup(self, deletion_plan):
        """
        将文件移动到备份目录
        """
        print("开始移动重复文件到备份目录...")
        success_count = 0
        error_count = 0

        for i, item in enumerate(deletion_plan, 1):
            source_path = Path(item['要删除的文件'])

            try:
                if not source_path.exists():
                    print(f"文件不存在，跳过: {source_path}")
                    error_count += 1
                    continue

                # 创建相对路径结构
                relative_path = source_path.relative_to(self.base_dir)
                backup_path = self.session_backup_dir / relative_path

                # 创建目标目录
                backup_path.parent.mkdir(parents=True, exist_ok=True)

                # 移动文件
                shutil.move(str(source_path), str(backup_path))

                # 记录操作日志
                self.operation_log.append({
                    '操作': '移动',
                    '源文件': str(source_path),
                    '目标位置': str(backup_path),
                    '时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    '状态': '成功'
                })

                success_count += 1
                if i % 100 == 0:
                    print(f"已处理 {i}/{len(deletion_plan)} 个文件...")

            except Exception as e:
                print(f"移动文件失败: {source_path} -> {e}")
                self.operation_log.append({
                    '操作': '移动',
                    '源文件': str(source_path),
                    '目标位置': '',
                    '时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    '状态': f'失败: {e}'
                })
                error_count += 1

        print(f"文件移动完成: 成功 {success_count} 个, 失败 {error_count} 个")
        return success_count, error_count

    def save_operation_log(self):
        """
        保存操作日志
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = self.base_dir / "tool" / f"操作日志_{timestamp}.csv"

            df_log = pd.DataFrame(self.operation_log)
            df_log.to_csv(log_file, index=False, encoding='utf-8-sig')

            print(f"操作日志已保存到: {log_file}")
            return log_file
        except Exception as e:
            print(f"保存操作日志失败: {e}")
            return None

    def generate_summary_report(self, deletion_plan, success_count, error_count):
        """
        生成汇总报告
        """
        print("\n" + "="*60)
        print("重复文件清理汇总报告")
        print("="*60)

        print(f"总重复文件组数: {len(self.duplicate_groups)}")
        print(f"计划删除文件数: {len(deletion_plan)}")
        print(f"成功移动文件数: {success_count}")
        print(f"移动失败文件数: {error_count}")

        # 按目录统计删除的文件
        dir_stats = {}
        for item in deletion_plan:
            for dir_name in self.dir_priority.keys():
                if dir_name in item['要删除的文件']:
                    dir_stats[dir_name] = dir_stats.get(dir_name, 0) + 1
                    break

        print("\n按目录统计删除的文件:")
        for dir_name, count in dir_stats.items():
            print(f"  {dir_name}: {count} 个文件")

        # 计算节省的空间
        total_size_saved = sum(item['文件大小'] for item in deletion_plan if success_count > 0)
        print(f"\n节省存储空间: {total_size_saved:.2f} MB")

        print(f"\n备份目录: {self.session_backup_dir}")
        print("="*60)

    def run(self):
        """
        执行重复文件清理
        """
        print("开始重复文件清理流程...")

        # 1. 加载数据
        if not self.load_csv_data():
            return False

        # 2. 分析重复文件
        self.analyze_duplicates()

        if not self.duplicate_groups:
            print("没有发现重复文件，无需清理。")
            return True

        # 3. 创建备份目录
        if not self.create_backup_directory():
            return False

        # 4. 生成删除计划
        deletion_plan = self.generate_deletion_plan()

        if not deletion_plan:
            print("没有需要删除的文件。")
            return True

        # 5. 保存删除计划
        plan_file = self.save_deletion_plan(deletion_plan)

        # 6. 用户确认
        print(f"\n即将删除 {len(deletion_plan)} 个重复文件")
        print("这些文件将被移动到备份目录，不会永久删除")

        # 显示前几个示例
        print("\n删除示例（前5个）:")
        for i, item in enumerate(deletion_plan[:5]):
            print(f"  {i+1}. {item['文件名']}")
            print(f"     删除: {item['要删除的文件']}")
            print(f"     保留: {item['保留的文件']}")
            print()

        # 自动执行（在实际使用中可以添加用户确认）
        print("开始执行删除操作...")

        # 7. 移动文件到备份目录
        success_count, error_count = self.move_files_to_backup(deletion_plan)

        # 8. 保存操作日志
        self.save_operation_log()

        # 9. 生成汇总报告
        self.generate_summary_report(deletion_plan, success_count, error_count)

        return True

def main():
    """
    主函数
    """
    # 设置路径
    base_dir = Path(__file__).parent.parent
    csv_file = base_dir / "tool" / "文件统计报告_20250821_111219.csv"

    # 检查CSV文件是否存在
    if not csv_file.exists():
        print(f"CSV文件不存在: {csv_file}")
        print("请确保CSV文件路径正确")
        return

    # 创建清理器并运行
    cleaner = DuplicateCleaner(csv_file, base_dir)
    cleaner.run()

if __name__ == "__main__":
    main()
